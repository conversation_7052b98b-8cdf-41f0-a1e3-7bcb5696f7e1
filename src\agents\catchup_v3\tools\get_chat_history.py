"""Chat history retrieval tool with conversation filtering capabilities."""

from typing import Optional, List, Tuple
from langchain_core.tools import tool
from langgraph.config import get_stream_writer

from agents.catchup_v3.supabase.client import supabase
from agents.catchup_v3.Models.model import ChatMessage

@tool
def get_chat_history(
    conversation_id: str,
    limit: Optional[int] = None
) -> Tuple[Optional[List[ChatMessage]], Optional[str]]:
    """Fetch chat history from the messages table filtered by conversation_id.

    This tool queries the Supabase 'messages' table to retrieve conversation history
    for a specific conversation_id. Messages are returned in descending order by
    created_at (latest first), with optional limit for the number of records.

    Args:
        conversation_id: The UUID of the conversation to get chat history from (required)
        limit: Optional integer to limit the number of messages returned.
               If None or 0, returns all messages for the conversation.

    Returns:
        Tuple containing (List[ChatMessage], error) where List[ChatMessage] contains
        the chat history data if successful, None if failed.
    """
    try:
        # Try to get stream writer, but handle gracefully if not in runnable context
        try:
            stream_writer = get_stream_writer()
            stream_writer({"custom_tool_call": f"invoking get_chat_history for conversation_id {conversation_id}, limit {limit}"})
        except Exception:
            # Not in a runnable context, continue without streaming
            pass

        # Build the query with conversation_id filter and ordering
        query = supabase.table('messages').select('*').eq('conversation_id', conversation_id).order('created_at', desc=True)

        # Apply limit if provided and greater than 0
        if limit and limit > 0:
            query = query.limit(limit)

        response = query.execute()

        # Extract data from response
        if response.data:
            # Map database records to ChatMessage models
            messages = []
            for item in response.data:
                message = ChatMessage(
                    id=item.get('id', ''),
                    conversation_id=item.get('conversation_id'),
                    user_id=item.get('user_id', ''),
                    content=item.get('content', ''),
                    created_at=item.get('created_at'),
                    read_at=item.get('read_at'),
                    metadata=item.get('metadata', {})
                )
                messages.append(message)

            return messages, None
        else:
            # No data returned - return empty list instead of None for consistency
            return [], None

    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)
