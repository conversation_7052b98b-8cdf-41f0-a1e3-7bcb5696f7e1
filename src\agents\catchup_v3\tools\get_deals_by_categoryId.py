"""Tool for retrieving deals by category while excluding deals owned by the current user."""

from typing import Optional, Any, List, Tuple, Annotated
from agents.catchup_v3.Models.model import Deal
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.config import get_stream_writer
import json
from pydantic import BaseModel
from datetime import datetime

from agents.catchup_v3.supabase.client import supabase



@tool
def get_deals_by_categoryId(
    category_id: str,
    user_id: str
) -> Tuple[Optional[List[Deal]], Optional[str]]:
    """Fetch deals from deals_dealid_view by category_id while excluding deals owned by the current user.

    This tool queries the Supabase view 'deals_dealid_view' to retrieve deals that match the specified
    category_id but excludes any deals where the owner_id equals the provided user_id. This is useful
    for showing users deals from other businesses/users in their desired category.

    Args:
        category_id: The ID of the category to filter deals by
        user_id: The ID of the current user (deals owned by this user will be excluded)

    Returns:
        Tuple containing (List[Deal], error) where List[Deal] contains the deals data
        if successful, None if failed.
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"invoking get_deals_by_categoryId for category_id {category_id}, excluding user {user_id}"})
        
        # Query deals_dealid_view with category filter and user exclusion
        response = supabase.table('deals_dealid_view').select('*').eq('category_id', category_id).neq('owner_id', user_id).execute()
        
        # Extract data from response
        if response.data:
            # Map database records to Deal models
            deals = []
            for item in response.data:
                deal = Deal(
                    id=item.get('id', ''),
                    title=item.get('title'),
                    description=item.get('description'),
                    category_id=item.get('category_id'),
                    owner_id=item.get('owner_id'),
                    price=item.get('price'),
                    discount_percentage=item.get('discount_percentage'),
                    business_name=item.get('business_name'),
                    location=item.get('location'),
                    availability=item.get('availability')
                )
                deals.append(deal)

            return deals, None
        else:
            # No data returned
            return [], None
            
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)



@tool
def search_deals(
    category_name: str,
    user_id: str
) -> Tuple[Optional[List[Deal]], Optional[str]]:
    """Fetch deals from deals_dealid_view by category_name while excluding deals owned by the current user.

    This tool queries the Supabase view 'deals_dealid_view' to retrieve deals that match the specified
    category_name but excludes any deals where the owner_id equals the provided user_id. This is useful
    for showing users deals from other businesses/users in their desired category.

    Args:
        category_name: The name of the category to filter deals by
        user_id: The ID of the current user (deals owned by this user will be excluded)

    Returns:
        Tuple containing (List[Deal], error) where List[Deal] contains the deals data
        if successful, None if failed.
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"invoking get_deals_by_categoryId for categor name {category_name}, excluding user {user_id}"})
        
        # Query deals_dealid_view with category filter and user exclusion
        response = supabase.table('deals_dealid_view').select('*').eq('category_name', category_name).neq('owner_id', user_id).execute()
        
        # Extract data from response
        if response.data:
            # Map database records to Deal models
            deals = []
            for item in response.data:
                deal = Deal(
                    id=item.get('id', ''),
                    title=item.get('title'),
                    description=item.get('description'),
                    category_id=item.get('category_id'),
                    owner_id=item.get('owner_id'),
                    price=item.get('price'),
                    discount_percentage=item.get('discount_percentage'),
                    business_name=item.get('business_name'),
                    location=item.get('location'),
                    availability=item.get('availability')
                )
                deals.append(deal)

            return deals, None
        else:
            # No data returned
            return [], None
            
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)




@tool
def get_deals(
    business_id: str
) -> Tuple[Optional[List[Deal]], Optional[str]]:
    """Fetch all deals and offers from a specific business or company.

    This tool queries the Supabase view 'deals_dealid_view' to retrieve all deals that belong to
    the specified business_id. This is useful for displaying all available deals from a particular
    business to customers browsing their offerings.

    Args:
        business_id: The ID of the business to retrieve deals for

    Returns:
        Tuple containing (List[Deal], error) where List[Deal] contains the deals data
        if successful, None if failed.
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"invoking get_deals for business_id {business_id}"})
        # Query deals_dealid_view with category filter and user exclusion
        response = supabase.table('deals_dealid_view').select('*').eq('business_id', business_id).execute()
        
        # Extract data from response
        if response.data:
            # Map database records to Deal models
            deals = []
            for item in response.data:
                deal = Deal(
                    id=item.get('id', ''),
                    title=item.get('title'),
                    description=item.get('description'),
                    category_id=item.get('category_id'),
                    owner_id=item.get('owner_id'),
                    price=item.get('price'),
                    discount_percentage=item.get('discount_percentage'),
                    business_name=item.get('business_name'),
                    location=item.get('location'),
                    availability=item.get('availability')
                )
                deals.append(deal)

            return deals, None
        else:
            # No data returned
            return [], None
            
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)
