"""Shared tools loading functionality for CatchUp agent."""

from typing import List
from langchain_core.tools import BaseTool
from shared.mcp_tools import get_catchup_tools_by_names
from agents.catchup_v3.tools import  get_user_details_by_id,get_all_categories,get_deals_by_categoryId ,search_deals,get_deals,get_chat_history,get_booking_details


MCP_TOOLS=[
        
        
        "get_business_details",
    
        
        "create_booking",
        "sent_email_to_users",
        "whatsapps_sent_tool",
    ]

async def get_all_catchup_tools() -> List[BaseTool]:
    """Get all available tools for CatchUp (MCP + local tools).
    
    Returns:
        List of all available tools for the CatchUp system
    """
    mcp_tools = await get_catchup_tools_by_names( MCP_TOOLS)
    print(f"MCP Tool names: {[tool.name for tool in mcp_tools]}")
    
    local_tools = [
                    get_user_details_by_id,
                    get_all_categories,
                    get_deals_by_categoryId,
                    search_deals,
                    get_deals,
                    get_chat_history,
                    get_booking_details
                ]
    print(f"Local Tool names: {[tool.name for tool in local_tools]}")
    all_tools = mcp_tools + local_tools
    
    print(f"Loaded {len(all_tools)} tools in total")
    print(f"Tool names: {[tool.name for tool in all_tools]}")
    
    return all_tools
