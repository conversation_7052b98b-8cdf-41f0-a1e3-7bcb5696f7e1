from langchain_core.messages import HumanMessage
from catchup_v3.hil import HILResponse

def handle_hil_response(agent_state, human_response: HILResponse):
    """Process human approval response and continue execution."""
    
    if human_response["approved"]:
        # Continue with original or modified action
        action = human_response.get("modified_action") or "proceed"
        return agent_state.update({
            "messages": [HumanMessage(content=f"Approved: {action}")]
        })
    else:
        # Reject action, ask subagent to try alternative
        feedback = human_response.get("feedback", "Action not approved")
        return agent_state.update({
            "messages": [HumanMessage(content=f"Not approved: {feedback}. Please try an alternative approach.")]
        })