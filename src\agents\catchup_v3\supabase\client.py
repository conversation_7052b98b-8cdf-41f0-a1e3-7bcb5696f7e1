"""Supabase client initialization and configuration."""

import sys
from pathlib import Path

# Add the src directory to Python path to access the original supabase client
current_dir = Path(__file__).parent
src_dir = current_dir.parent.parent.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

# Import the original supabase client
from catchup.supabase.client import supabase
