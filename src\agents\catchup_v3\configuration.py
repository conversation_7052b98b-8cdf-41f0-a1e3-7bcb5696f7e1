"""Define the configurable parameters for the agent."""

from __future__ import annotations
from dataclasses import dataclass, field, fields
from typing import Annotated, Optional
from langchain_core.runnables import RunnableConfig, ensure_config

@dataclass(kw_only=True)
class Configuration:
    """The configuration for the agent."""

    model_name_enhancer_node: Annotated[str, {"__template_metadata__": {"kind": "llm"}}] = field(
        default="google/gemma-3-4b-it:free",
        metadata={
            "description": "The name of the language model to use for the agent. "
            "Should be in the form: provider/model-name."
        },
    )


    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> Configuration:
        """Load configuration w/ defaults for the given invocation."""
        config = ensure_config(config)
        configurable = config.get("configurable") or {}
        _fields = {f.name for f in fields(cls) if f.init}
        return cls(**{k: v for k, v in configurable.items() if k in _fields})
