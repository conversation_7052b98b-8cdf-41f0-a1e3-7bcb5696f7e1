import os
from agents.catchup_v3.prompts import create_system_prompt
from langchain_core.messages import SystemMessage
from langchain_core.runnables import RunnableConfig
from typing import Any, Dict
from datetime import datetime

from agents.catchup_v3.state import CatchUpState
from shared import create_llm
from langgraph.types import StreamWriter
from agents.catchup_v3.tools_loader import get_all_catchup_tools
# from langchain_anthropic import ChatAnthropic
# from langchain_openai import Chat<PERSON><PERSON>A<PERSON>

async def call_model(state: CatchUpState,writer: StreamWriter, config: RunnableConfig) -> Dict[str, Any]:
    """Enhanced call_model with better state management."""
    
    #print("call_model", state)
    configuration = config.get("configurable", {})
    model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
   
    
   ## ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY')
    # Create LLM instance
    llm = create_llm(model_name)
    #llm = ChatOpenAI(model='gpt-4-turbo-preview')
    
    # Get tools
   
    tools = await get_all_catchup_tools()
    
    # Bind tools without provider-specific parameters that might not be supported
    llm_with_tools = llm.bind_tools(tools)
    
    # Use existing system prompt function
    system_prompt = create_system_prompt(state)
    
    # Prepare messages with system prompt
    messages = [SystemMessage(content=system_prompt)] + state["messages"]
    
    # Filter out incomplete tool call sequences to prevent API errors
    filtered_messages = []
    i = 0
    while i < len(messages):
        msg = messages[i]
        
        # Handle AIMessage with tool_calls
        if hasattr(msg, 'tool_calls') and msg.tool_calls:
            # Check if next message is a corresponding ToolMessage
            next_msg = messages[i + 1] if i + 1 < len(messages) else None
            if next_msg and hasattr(next_msg, 'tool_call_id'):
                # Include both the tool call and its result as a pair
                filtered_messages.append(msg)
                filtered_messages.append(next_msg)
                i += 2  # Skip both messages
            else:
                # Skip incomplete tool call (no result)
                i += 1
        # Handle standalone ToolMessage (orphaned - skip it)
        elif hasattr(msg, 'tool_call_id'):
            i += 1  # Skip orphaned tool result
        else:
            # Regular message - include it
            filtered_messages.append(msg)
            i += 1
    
    # Generate response
    try:
        response = await llm_with_tools.ainvoke(filtered_messages)
        return {"messages": [response]}
        
    except Exception as e:
        print(f"Error in call_model: {e}")
        # Return error response
        from langchain_core.messages import AIMessage
        error_response = AIMessage(
            content="I apologize, but I encountered an error while processing your request. Please try again."
        )
        return {"messages": [error_response]}
