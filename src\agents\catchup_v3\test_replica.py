"""Test script to verify catchup_v3 is an exact replica of the original catchup agent."""

import asyncio
from langchain_core.messages import HumanMessage
from agents.catchup_v3.graph import graph
from agents.catchup_v3.state import CatchUpState

async def test_catchup_v3():
    """Test the catchup_v3 agent with a simple query."""
    
    # Create test state
    test_state: CatchUpState = {
        "messages": [HumanMessage(content="Hello, I need help finding deals for restaurants in Milan")],
        "user_id": "test_user_123",
        "session_id": "test_session_456",
        "latitude": "45.4642",
        "longitude": "9.1900",
        "email_address": "<EMAIL>",
        "isChat": True
    }
    
    # Test configuration
    config = {
        "configurable": {
            "model_name": "anthropic/claude-3.5-sonnet"
        }
    }
    
    print("Testing CatchUp v3 agent...")
    print(f"Input: {test_state['messages'][0].content}")
    print("=" * 50)
    
    try:
        # Run the graph
        result = await graph.ainvoke(test_state, config)
        
        print("Response received successfully!")
        print(f"Number of messages in result: {len(result.get('messages', []))}")
        
        if result.get('messages'):
            last_message = result['messages'][-1]
            print(f"Last message type: {type(last_message).__name__}")
            print(f"Last message content preview: {str(last_message.content)[:200]}...")
            
            # Check if it has tool calls
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                print(f"Tool calls found: {len(last_message.tool_calls)}")
                for i, tool_call in enumerate(last_message.tool_calls):
                    print(f"  Tool {i+1}: {tool_call.get('name', 'unknown')}")
        
        print("\n✅ CatchUp v3 agent is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing CatchUp v3 agent: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_catchup_v3())
